/*
 * Created Date: 2/01/2024 10:33:50
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 12th November 2024 16:52:25
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_core/models/comment/comment.dart';
import 'package:gp_core_v2/base/usecase/model/model.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/cancel/ticket_cancel.params.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/tag/ticket_tag.params.dart';
import 'package:gp_feat_ticket/data/model/model.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/list/assignee/ticket_list_assignee_params.dart';

import '../../data/data.dart';
import '../domain.dart';

/// GapoTicket repository
abstract class TicketRepository {
  /// Không có docs
  Future<ListAPIResponseV2<TicketListResponse>> tickets(
    TicketListParams params,
  );

  /// Không có docs
  Future<ApiResponseV2<TicketListResponse>> details(
    String ticketId,
  );

  /// Không có docs
  Future<ListAPIResponseV2<TicketListResponse>> relativeTickets(
    TicketListRelativeParams params,
  );

  /// Không có docs
  Future<ApiResponseV2<TicketListResponse>> createTickets(
    TicketCreateParams params,
  );

  /// Không có docs
  Future<ApiResponseV2<TicketFlowChartResponse>> flowCharts({
    required String ticketId,
  });

  /// Không có docs
  Future<ApiResponseV2<TicketNodeResponse>> nodes({
    required String ticketId,
    required String nodeId,
  });

  /// Không có docs
  Future<ApiResponseV2<dynamic>> assignee({
    required TicketUpdateAssigneeInput assigneeInput,
  });

  /// Không có docs
  Future<ApiResponseV2<dynamic>> follower({
    required TicketAddFollowerInput followerInput,
  });

  /// Không có docs
  Future<void> addFollowerAllStep({
    required TicketAddFollowerInput followerInput,
  });

  /// Không có docs
  Future<ApiResponseV2<dynamic>> additionalRequests({
    required TicketAdditionalRequestsParams params,
  });

  /// Không có docs
  Future<ApiResponseV2<TicketAdditionalRequestResponse>>
      responseAdditionalRequests({
    required String ticketId,
    required String nodeId,
    required String additionalRequestId,
    required TicketResponseAdditionalRequestParams params,
  });

  /// Không có docs
  Future<void> fields({
    required String ticketId,
    required TicketUpdateFieldValuesParams params,
  });

  /// Không có docs
  Future<ApiResponseV2<TicketNodeResponse>> status({
    required String ticketId,
    required String nodeId,
    required TicketUpdateNodeStatusParams params,
  });

  /// Không có docs
  Future<ApiResponseV2<dynamic>> spam({
    required String ticketId,
    required String nodeId,
    required TicketSpamParams params,
  });

  /// Không có docs
  Future<ApiResponseV2<dynamic>> moveToOnHold({
    required String ticketId,
    required String nodeId,
    required TicketMoveToOnHoldParams params,
  });

  /// Không có docs
  Future<ListAPIResponseV2<TicketAdditionalRequestResponse>>
      getAdditionalRequest({
    required String ticketId,
    required String nodeId,
  });

  Future<ListAPIResponseV2<TicketActivityResponse>> getActivities(
      {required String ticketId});

  Future<ApiResponseV2<TicketActivityResponse>> getActivityDetail({
    required String ticketId,
    required String activityId,
  });

  Future<ApiResponseV2<TicketSpamRequestResponse>> getSpamDetail({
    required String ticketId,
    required String nodeId,
    required String spamId,
  });

  /// Không có docs
  Future<ListAPIResponseV2<TicketOnHoldRequestResponse>> getOnHoldRequest({
    required String ticketId,
    required String nodeId,
  });

  /// Không có docs
  Future<void> acceptOnHold({
    required String ticketId,
    required String nodeId,
    required String onHoldId,
  });

  /// Không có docs
  Future<void> rejectOnHold({
    required String ticketId,
    required String nodeId,
    required String onHoldId,
  });

  Future<void> cancelOnHold({
    required String ticketId,
    required String nodeId,
    required String onHoldId,
  });

  Future<ListAPIResponse<Assignee>> members(Map<String, dynamic> queries);

  Future<ListAPIResponse<Assignee>> getFollowMembers({
    required String ticketId,
    required TicketFollowMembersParams params,
  });
  Future<ListAPIResponseV2<TicketCommentResponse>> comments({
    required String ticketId,
    required String nodeId,
    required TicketCommentsParams params,
  });

  Future<ApiResponseV2<TicketCommentResponse>> postComment({
    required TicketPostCommentsRequestParams params,
  });

  Future<ApiResponseV2<Comment>> editComment({
    required TicketEditCommentsRequestParams params,
    required String commentId,
  });

  Future<ApiResponseV2WithoutData> deleteComment({
    required String commentId,
  });

  Future<void> unfollow({
    required String ticketId,
    required String nodeId,
  });

  Future<void> deleteTicket({
    required String ticketId,
  });

  Future<void> reopen({
    required String ticketId,
    required TicketReopenParams params,
  });

  Future<void> close({
    required String ticketId,
    required bool hasReview,
  });

  Future<ApiResponseV2<TicketListResponse>> review({
    required String ticketId,
    required TicketReviewParams params,
  });

  Future<void> cancel({
    required String ticketId,
    required TicketCancelParams params,
  });

  Future<void> labels({
    required String ticketId,
    required String nodeId,
    required TicketLabelParams params,
  });

  Future<ApiResponseV2<TicketIsTicketAssigneeResponse>> isAssignee({
    required String ticketId,
  });

  /// Get nodes that can be redone
  Future<ListAPIResponseV2<TicketNodeResponse>> getCanRedoNodes({
    required String ticketId,
    required String nodeId,
  });

  /// Get end node that can be redone
  Future<ListAPIResponseV2<TicketNodeResponse>> getCanRedoEndNode({
    required String ticketId,
  });

  /// Redo end nodes
  Future<void> redoEndNodes({
    required String ticketId,
    required TicketRedoEndNodeParams params,
  });

  /// Redo previous steps
  Future<void> redoPrevious({
    required String ticketId,
    required String nodeId,
    required TicketRedoPreviousParams params,
  });

  /// Get node assignees
  Future<ListAPIResponseV2<Assignee>> getNodeAssignees({
    required String ticketId,
    required String nodeId,
    required TicketListAssigneeParams params,
  });
}
