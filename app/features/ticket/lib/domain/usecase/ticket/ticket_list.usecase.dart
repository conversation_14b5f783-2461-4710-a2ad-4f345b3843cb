/*
 * Created Date: Monday, 22nd July 2024, 11:44:01
 * Author: gapo
 * -----
 * Last Modified: Monday, 16th September 2024 16:26:10
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketListUseCase extends GPBaseFutureUseCase<TicketListInput,
    ListAPIResponseV2<TicketListResponse>> {
  TicketListUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ListAPIResponseV2<TicketListResponse>> buildUseCase(
    TicketListInput input,
  ) async {
    return _ticketRepository.tickets(input.params);
  }
}

class TicketListInput extends GPBaseInput {
  const TicketListInput({
    required this.params,
  });

  final TicketListParams params;
}
